// Test the actual API endpoint with real file upload
import fs from 'fs';
import FormData from 'form-data';
import fetch from 'node-fetch';

console.log("🧪 Testing REAL API Upload");
console.log("=" .repeat(40));

async function testRealUpload() {
  try {
    // Check if there's an uploaded file to test with
    const uploadsDir = './public/uploads';
    const files = fs.readdirSync(uploadsDir);
    const imageFiles = files.filter(f => f.endsWith('.jpeg') || f.endsWith('.jpg') || f.endsWith('.png'));
    
    if (imageFiles.length === 0) {
      console.log("❌ No uploaded images found. Please upload an image first!");
      return;
    }
    
    const testFile = imageFiles[0];
    const filePath = `${uploadsDir}/${testFile}`;
    
    console.log(`📸 Testing with: ${testFile}`);
    
    // Create form data
    const formData = new FormData();
    formData.append('file', fs.createReadStream(filePath), {
      filename: testFile,
      contentType: 'image/jpeg'
    });
    
    console.log("🚀 Sending request to API...");
    
    // Make API request
    const response = await fetch('http://localhost:8082/api/v1/plant/mlmodel', {
      method: 'POST',
      body: formData
    });
    
    const result = await response.json();
    
    console.log("\n📋 API Response:");
    console.log(JSON.stringify(result, null, 2));
    
    if (result.success) {
      console.log("\n✅ SUCCESS!");
      console.log(`🌿 Identified Plant: ${result.name}`);
      console.log(`📊 Confidence: ${result.confidence}%`);
      console.log(`🔧 Method: ${result.method}`);
      
      if (result.analysis_details) {
        console.log(`📝 Original Filename: ${result.analysis_details.filename}`);
        console.log(`📏 File Size: ${(result.analysis_details.filesize / 1024).toFixed(1)}KB`);
      }
    } else {
      console.log("\n❌ FAILED!");
      console.log(`Error: ${result.message}`);
    }
    
  } catch (error) {
    console.error("❌ Test failed:", error.message);
  }
}

// Also test what plants are in the database
async function checkDatabase() {
  try {
    console.log("\n🗄️ Checking Database Plants:");
    
    const response = await fetch('http://localhost:8082/api/v1/plant/Aloe%20Vera');
    const result = await response.json();
    
    if (result.success) {
      console.log("✅ Database connection working");
      console.log(`📋 Sample plant: ${result.plant.scientificName}`);
      console.log(`🏷️ Local name: ${result.plant.localName}`);
      console.log(`💊 Features: ${result.plant.features}`);
    } else {
      console.log("❌ Database issue:", result.message);
    }
    
  } catch (error) {
    console.error("❌ Database check failed:", error.message);
  }
}

// Run tests
console.log("1️⃣ Testing API Upload...");
await testRealUpload();

console.log("\n2️⃣ Testing Database...");
await checkDatabase();

console.log("\n💡 If the results are wrong:");
console.log("• Check backend logs for detailed analysis");
console.log("• Verify the plant is in the database");
console.log("• Check if filename analysis is working");
console.log("• Ensure scientific name mapping is correct");
