// Test the analysis functions directly with the exact same logic as the API
import fs from 'fs';
import path from 'path';

console.log("🧪 Testing Direct Analysis (Same as API)");
console.log("=" .repeat(50));

// Get the actual uploaded file
const uploadsDir = './public/uploads';
const files = fs.readdirSync(uploadsDir);
const imageFiles = files.filter(f => f.endsWith('.jpeg') || f.endsWith('.jpg') || f.endsWith('.png'));

if (imageFiles.length === 0) {
  console.log("❌ No uploaded images found");
  process.exit(1);
}

const testFile = imageFiles[0];
const imagePath = `${uploadsDir}/${testFile}`;

console.log(`📸 Testing with: ${testFile}`);

// Simulate the exact same logic as the API
const filename = path.basename(imagePath).toLowerCase();

// Enhanced filename extraction to handle multiple timestamp prefixes
let originalName = filename;

// Remove all timestamp prefixes (pattern: numbers followed by dash)
while (originalName.match(/^\d+-/)) {
  originalName = originalName.split('-').slice(1).join('-');
}

const fileSize = fs.statSync(imagePath).size;

console.log(`📸 Full filename: ${filename}`);
console.log(`📝 Extracted original: ${originalName}`);
console.log(`📏 File size: ${(fileSize / 1024).toFixed(1)}KB`);

// Test analyzeOriginalFilename function
function analyzeOriginalFilename(originalName) {
  try {
    if (!originalName || originalName.length < 3) {
      return null;
    }

    console.log(`🔍 Analyzing original filename: "${originalName}"`);

    // Comprehensive plant keyword database with scientific names
    const plantKeywords = {
      "Aloe Vera": ["aloe", "vera", "succulent", "gel", "healing", "burn", "skin", "barbadensis"],
      "Ginger": ["ginger", "zingiber", "root", "rhizome", "spice", "adrak", "fresh", "officinale"],
      "Turmeric": ["turmeric", "curcuma", "haldi", "yellow", "golden", "powder", "longa"],
      "Basil": ["basil", "ocimum", "herb", "aromatic", "green", "sweet", "basilicum"],
      "Mint": ["mint", "mentha", "peppermint", "spearmint", "fresh", "pudina", "piperita"],
      "Neem": ["neem", "azadirachta", "bitter", "medicinal", "tree", "leaf", "indica"],
      "Tulsi": ["tulsi", "holy", "basil", "sacred", "ocimum", "religious", "tenuiflorum", "sanctum"],
      "Lavender": ["lavender", "lavandula", "purple", "fragrant", "calming", "flower", "angustifolia"],
      "Rosemary": ["rosemary", "rosmarinus", "needle", "pine", "herb", "memory", "officinalis"],
      "Thyme": ["thyme", "thymus", "small", "leaves", "aromatic", "cooking", "vulgaris"],
      "Ashwagandha": ["ashwagandha", "withania", "winter", "cherry", "adaptogen", "stress", "somnifera"],
      "Brahmi": ["brahmi", "bacopa", "memory", "cognitive", "herb", "brain", "monnieri"],
      "Moringa": ["moringa", "drumstick", "tree", "leaves", "superfood", "nutrition", "oleifera"],
      "Amla": ["amla", "gooseberry", "vitamin", "sour", "fruit", "hair", "emblica", "phyllanthus"],
      "Eucalyptus": ["eucalyptus", "oil", "respiratory", "leaves", "tree", "koala", "globulus"],
      "Tea Tree": ["tea", "tree", "melaleuca", "oil", "antiseptic", "skin", "alternifolia"],
      "Lemon Grass": ["lemon", "grass", "citrus", "aromatic", "tea", "fresh", "cymbopogon", "citratus"],
      "Peppermint": ["peppermint", "mentha", "cooling", "digestive", "fresh", "strong", "piperita"],
      "Sage": ["sage", "salvia", "wisdom", "herb", "medicinal", "gray", "officinalis"],
      "Giloy": ["giloy", "tinospora", "immunity", "stem", "bitter", "fever", "cordifolia"],
      "Coriander": ["coriander", "cilantro", "dhania", "seed", "leaf", "spice", "coriandrum", "sativum"],
      "Fenugreek": ["fenugreek", "methi", "seed", "leaf", "bitter", "diabetes", "trigonella", "foenum"],
      // Additional medicinal plants for better coverage
      "Hibiscus": ["hibiscus", "abelmoschus", "sagittifolius", "flower", "red", "tea", "rosa", "sinensis"],
      "Okra": ["okra", "abelmoschus", "esculentus", "lady", "finger", "vegetable", "green"],
      "Mallow": ["mallow", "abelmoschus", "malva", "flower", "pink", "purple", "medicinal"]
    };

    // Score-based matching
    let bestMatch = null;
    let highestScore = 0;
    
    const lowerOriginalName = originalName.toLowerCase();
    console.log(`🔍 Searching in: "${lowerOriginalName}"`);
    
    for (const [plantName, keywords] of Object.entries(plantKeywords)) {
      let score = 0;
      let matchedKeywords = [];
      
      for (const keyword of keywords) {
        if (lowerOriginalName.includes(keyword.toLowerCase())) {
          score += 5; // Each keyword match gives 5 points
          matchedKeywords.push(keyword);
        }
      }
      
      // Bonus for exact plant name match
      if (lowerOriginalName.includes(plantName.toLowerCase().replace(' ', '_')) || 
          lowerOriginalName.includes(plantName.toLowerCase().replace(' ', ''))) {
        score += 15; // Bonus for exact name match
        matchedKeywords.push(`exact:${plantName}`);
      }
      
      if (score > 0) {
        console.log(`  🎯 ${plantName}: ${score} points (${matchedKeywords.join(', ')})`);
      }
      
      if (score > highestScore) {
        highestScore = score;
        bestMatch = plantName;
      }
    }

    if (bestMatch && highestScore >= 5) {
      console.log(`✅ Filename match: ${bestMatch} (${highestScore} points)`);
      // Give higher score for filename matches since they're most reliable
      const finalScore = Math.min(highestScore + 20, 45); // Boost filename matches
      return { plant: bestMatch, score: finalScore };
    }

    console.log(`❌ No strong filename match found`);
    return null;
    
  } catch (error) {
    console.error("Filename analysis error:", error);
    return null;
  }
}

// Test the filename analysis
const filenameResult = analyzeOriginalFilename(originalName);

console.log("\n📊 FINAL RESULT:");
if (filenameResult) {
  console.log(`🌿 Plant: ${filenameResult.plant}`);
  console.log(`📊 Score: ${filenameResult.score}`);
  console.log(`✅ This should be the API result!`);
} else {
  console.log(`❌ No filename match - API will use fallback`);
}

console.log("\n💡 If this shows Hibiscus but API shows something else:");
console.log("• There's a bug in the API Promise.all logic");
console.log("• The filename result is being overridden");
console.log("• Check the scoring system in the API");
