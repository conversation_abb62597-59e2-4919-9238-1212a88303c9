// Debug filename extraction
console.log("🔍 Testing Filename Extraction Logic");
console.log("=" .repeat(50));

// Test the filename extraction logic
function testFilenameExtraction(filename) {
  console.log(`\n📸 Testing: ${filename}`);
  
  let originalName = filename;
  
  // Remove all timestamp prefixes (pattern: numbers followed by dash)
  while (originalName.match(/^\d+-/)) {
    console.log(`  Removing prefix from: ${originalName}`);
    originalName = originalName.split('-').slice(1).join('-');
    console.log(`  Result: ${originalName}`);
  }
  
  console.log(`✅ Final extracted name: ${originalName}`);
  
  // Test scientific name recognition
  if (originalName.toLowerCase().includes('abelmoschus')) {
    console.log(`🔬 Scientific name 'abelmoschus' detected → Should map to Hibiscus`);
  }
  
  if (originalName.toLowerCase().includes('sagittifolius')) {
    console.log(`🔬 Scientific name 'sagittifolius' detected → Should map to Hibiscus`);
  }
  
  return originalName;
}

// Test cases
const testFiles = [
  "1754676422704-1754672260211-<PERSON><PERSON><PERSON>_sagittifolius_00755.jpeg",
  "1754672260211-Abelmoschus_sagittifolius_00755.jpeg",
  "Abelmoschus_sagittifolius_00755.jpeg",
  "aloe_vera_plant.jpg",
  "1234567890-ginger_root.png"
];

testFiles.forEach(testFilenameExtraction);

console.log("\n🧠 Testing Scientific Name Mapping:");

const scientificNameMap = {
  "abelmoschus": "Hibiscus",
  "sagittifolius": "Hibiscus", 
  "esculentus": "Okra",
  "zingiber": "Ginger",
  "curcuma": "Turmeric",
  "ocimum": "Tulsi",
  "azadirachta": "Neem",
  "withania": "Ashwagandha",
  "bacopa": "Brahmi",
  "moringa": "Moringa"
};

const testName = "abelmoschus_sagittifolius_00755.jpeg";
console.log(`\n🔬 Testing scientific name recognition for: ${testName}`);

const lowerName = testName.toLowerCase();
for (const [scientificPart, plantName] of Object.entries(scientificNameMap)) {
  if (lowerName.includes(scientificPart)) {
    console.log(`✅ Found: ${scientificPart} → ${plantName}`);
  }
}

console.log("\n💡 The issue might be:");
console.log("1. Filename extraction not working correctly");
console.log("2. Scientific name mapping not being called");
console.log("3. Intelligent selection overriding the correct result");
console.log("4. Analysis functions not being called in the right order");
