import bodyParser from "body-parser";
import express from "express";
import multer from "multer";
import {
    getFeedback,
    getPlantController,
    getResultName,
    postFeedback,
    uploadPlantController,
} from "../controller/plantController.js";

// Router object
const router = express.Router();

// Middleware for parsing form data
router.use(bodyParser.urlencoded({ extended: true }));
router.use(express.static("public"));

// Multer storage configuration
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, "public/uploads"); // Ensures correct path for uploaded files
  },
  filename: (req, file, cb) => {
    cb(null, `${Date.now()}-${file.originalname}`);
  },
});

// Multer file filter (Only accept images)
const fileFilter = (req, file, cb) => {
  if (file.mimetype.startsWith("image/")) {
    cb(null, true);
  } else {
    cb(new Error("Only image files are allowed!"), false);
  }
};

// Upload settings (Size limit: 50MB)
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: { fileSize: 50 * 1024 * 1024 }, // 50MB limit
});

// Routes
router.post("/create", upload.single("file"), uploadPlantController);
router.get("/feedback", getFeedback);
router.post("/feedback", postFeedback);
router.post("/mlmodel", upload.single("file"), getResultName);
router.get("/:name", getPlantController); // Placed last to avoid conflicts

export default router;