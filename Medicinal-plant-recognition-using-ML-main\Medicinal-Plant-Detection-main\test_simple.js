// Simple test to verify the analysis functions work correctly
console.log("🧪 Testing Analysis Functions Directly");
console.log("=" .repeat(50));

// Test the analyzeOriginalFilename function directly
function testAnalyzeOriginalFilename(originalName) {
  console.log(`\n🔍 Testing: "${originalName}"`);
  
  if (!originalName || originalName.length < 3) {
    console.log("❌ Name too short or empty");
    return null;
  }

  console.log(`🔍 Analyzing original filename: "${originalName}"`);

  // Comprehensive plant keyword database with scientific names
  const plantKeywords = {
    "Aloe Vera": ["aloe", "vera", "succulent", "gel", "healing", "burn", "skin", "barbadensis"],
    "Ginger": ["ginger", "zingiber", "root", "rhizome", "spice", "adrak", "fresh", "officinale"],
    "Turmeric": ["turmeric", "curcuma", "haldi", "yellow", "golden", "powder", "longa"],
    "Basil": ["basil", "ocimum", "herb", "aromatic", "green", "sweet", "basilicum"],
    "Mint": ["mint", "mentha", "peppermint", "spearmint", "fresh", "pudina", "piperita"],
    "Neem": ["neem", "azadirachta", "bitter", "medicinal", "tree", "leaf", "indica"],
    "Tulsi": ["tulsi", "holy", "basil", "sacred", "ocimum", "religious", "tenuiflorum", "sanctum"],
    "Lavender": ["lavender", "lavandula", "purple", "fragrant", "calming", "flower", "angustifolia"],
    "Rosemary": ["rosemary", "rosmarinus", "needle", "pine", "herb", "memory", "officinalis"],
    "Thyme": ["thyme", "thymus", "small", "leaves", "aromatic", "cooking", "vulgaris"],
    "Ashwagandha": ["ashwagandha", "withania", "winter", "cherry", "adaptogen", "stress", "somnifera"],
    "Brahmi": ["brahmi", "bacopa", "memory", "cognitive", "herb", "brain", "monnieri"],
    "Moringa": ["moringa", "drumstick", "tree", "leaves", "superfood", "nutrition", "oleifera"],
    "Amla": ["amla", "gooseberry", "vitamin", "sour", "fruit", "hair", "emblica", "phyllanthus"],
    "Eucalyptus": ["eucalyptus", "oil", "respiratory", "leaves", "tree", "koala", "globulus"],
    "Tea Tree": ["tea", "tree", "melaleuca", "oil", "antiseptic", "skin", "alternifolia"],
    "Lemon Grass": ["lemon", "grass", "citrus", "aromatic", "tea", "fresh", "cymbopogon", "citratus"],
    "Peppermint": ["peppermint", "mentha", "cooling", "digestive", "fresh", "strong", "piperita"],
    "Sage": ["sage", "salvia", "wisdom", "herb", "medicinal", "gray", "officinalis"],
    "Giloy": ["giloy", "tinospora", "immunity", "stem", "bitter", "fever", "cordifolia"],
    "Coriander": ["coriander", "cilantro", "dhania", "seed", "leaf", "spice", "coriandrum", "sativum"],
    "Fenugreek": ["fenugreek", "methi", "seed", "leaf", "bitter", "diabetes", "trigonella", "foenum"],
    // Additional medicinal plants for better coverage
    "Hibiscus": ["hibiscus", "abelmoschus", "sagittifolius", "flower", "red", "tea", "rosa", "sinensis"],
    "Okra": ["okra", "abelmoschus", "esculentus", "lady", "finger", "vegetable", "green"],
    "Mallow": ["mallow", "abelmoschus", "malva", "flower", "pink", "purple", "medicinal"]
  };

  // Score-based matching
  let bestMatch = null;
  let highestScore = 0;
  
  const lowerOriginalName = originalName.toLowerCase();
  console.log(`🔍 Searching in: "${lowerOriginalName}"`);
  
  for (const [plantName, keywords] of Object.entries(plantKeywords)) {
    let score = 0;
    let matchedKeywords = [];
    
    for (const keyword of keywords) {
      if (lowerOriginalName.includes(keyword.toLowerCase())) {
        score += 5; // Each keyword match gives 5 points
        matchedKeywords.push(keyword);
      }
    }
    
    // Bonus for exact plant name match
    if (lowerOriginalName.includes(plantName.toLowerCase().replace(' ', '_')) || 
        lowerOriginalName.includes(plantName.toLowerCase().replace(' ', ''))) {
      score += 15; // Bonus for exact name match
      matchedKeywords.push(`exact:${plantName}`);
    }
    
    if (score > 0) {
      console.log(`  🎯 ${plantName}: ${score} points (${matchedKeywords.join(', ')})`);
    }
    
    if (score > highestScore) {
      highestScore = score;
      bestMatch = plantName;
    }
  }

  if (bestMatch && highestScore >= 5) {
    console.log(`✅ Best match: ${bestMatch} (${highestScore} points)`);
    return { plant: bestMatch, score: Math.min(highestScore, 25) };
  }

  console.log(`❌ No match found`);
  return null;
}

// Test cases
const testCases = [
  "Abelmoschus_sagittifolius_00755.jpeg",
  "abelmoschus_sagittifolius_00755.jpeg", 
  "hibiscus_flower.jpg",
  "aloe_vera_plant.jpg",
  "ginger_root.png",
  "IMG_001.jpg"
];

testCases.forEach(testAnalyzeOriginalFilename);

console.log("\n💡 Key Insights:");
console.log("• 'abelmoschus' should match Hibiscus");
console.log("• 'sagittifolius' should match Hibiscus");
console.log("• Both keywords are in the Hibiscus array");
console.log("• The function should return Hibiscus with high score");

console.log("\n🔧 If this works but the API doesn't:");
console.log("• The issue is in the API flow, not the analysis function");
console.log("• Check if the function is being called correctly");
console.log("• Check if the result is being processed correctly");
