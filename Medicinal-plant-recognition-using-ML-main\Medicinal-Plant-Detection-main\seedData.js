import fs from 'fs';
import mongoose from 'mongoose';
import dotenv from 'dotenv';
import plantModel from './models/plantModel.js';
import path from 'path';
import { fileURLToPath } from 'url';

// Config dotenv
dotenv.config();

// Get directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI)
  .then(() => console.log('Connected to MongoDB for seeding'))
  .catch(err => console.error('MongoDB connection error:', err));

// Read plant data from JSON file
const plantData = JSON.parse(
  fs.readFileSync(path.join(__dirname, 'data', 'plants.json'), 'utf-8')
);

// Import data into database
const importData = async () => {
  try {
    // Clear existing data
    await plantModel.deleteMany({});
    console.log('Existing plant data cleared');
    
    // Insert new data
    await plantModel.insertMany(plantData);
    console.log(`${plantData.length} plants imported successfully`);
    
    process.exit(0);
  } catch (error) {
    console.error('Error importing data:', error);
    process.exit(1);
  }
};

// Run the import
importData();