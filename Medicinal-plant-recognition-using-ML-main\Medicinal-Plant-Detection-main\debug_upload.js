// Debug script to test actual image upload processing
import fs from 'fs';
import path from 'path';

console.log("🔍 DEBUGGING ACTUAL IMAGE UPLOADS");
console.log("=" .repeat(50));

// Check the actual uploaded files
const uploadsDir = './public/uploads';
const files = fs.readdirSync(uploadsDir);

console.log("📁 Files in uploads directory:");
files.forEach(file => {
  if (file.endsWith('.jpeg') || file.endsWith('.jpg') || file.endsWith('.png')) {
    const filePath = path.join(uploadsDir, file);
    const stats = fs.statSync(filePath);
    console.log(`  📸 ${file}`);
    console.log(`     Size: ${(stats.size / 1024).toFixed(1)}KB`);
    console.log(`     Created: ${stats.birthtime.toLocaleString()}`);
    
    // Test our filename extraction logic
    const originalName = file.split('-').slice(1).join('-');
    console.log(`     Original name: ${originalName}`);
    
    // Test our analysis functions
    testAnalysis(file, originalName, stats.size);
    console.log("  " + "-".repeat(40));
  }
});

function testAnalysis(filename, originalName, fileSize) {
  console.log(`  🔬 Testing analysis for: ${originalName}`);
  
  // Test filename analysis
  const filenameResult = analyzeOriginalFilename(originalName);
  if (filenameResult) {
    console.log(`    📝 Filename match: ${filenameResult.plant} (+${filenameResult.score} points)`);
  } else {
    console.log(`    📝 No filename match found`);
  }
  
  // Test file size analysis
  const sizeResult = analyzeImageSize(fileSize);
  console.log(`    📏 Size analysis: ${sizeResult.plant} (+${sizeResult.score} points)`);
  
  // Test format analysis
  const formatResult = analyzeImageFormat(filename);
  console.log(`    🖼️ Format analysis: ${formatResult.plant} (+${formatResult.score} points)`);
  
  // Calculate total score
  let totalScore = (filenameResult?.score || 0) + sizeResult.score + formatResult.score;
  let bestPlant = filenameResult?.plant || sizeResult.plant || formatResult.plant;
  
  if (!bestPlant || totalScore < 15) {
    const fallback = intelligentPlantSelection(originalName, fileSize);
    bestPlant = fallback.plant;
    totalScore += fallback.score;
    console.log(`    🧠 Intelligent fallback: ${fallback.plant} (+${fallback.score} points)`);
  }
  
  const confidence = Math.min(totalScore / 100, 0.95);
  console.log(`    🏆 RESULT: ${bestPlant} (${totalScore}/100, ${(confidence*100).toFixed(1)}% confidence)`);
}

// Copy the analysis functions from the controller
function analyzeOriginalFilename(originalName) {
  try {
    if (!originalName || originalName.length < 3) {
      return null;
    }
    
    const plantKeywords = {
      "Aloe Vera": ["aloe", "vera", "succulent", "gel", "healing", "burn", "skin"],
      "Ginger": ["ginger", "zingiber", "root", "rhizome", "spice", "adrak", "fresh"],
      "Turmeric": ["turmeric", "curcuma", "haldi", "yellow", "golden", "powder"],
      "Basil": ["basil", "ocimum", "herb", "aromatic", "green", "sweet"],
      "Mint": ["mint", "mentha", "peppermint", "spearmint", "fresh", "pudina"],
      "Neem": ["neem", "azadirachta", "bitter", "medicinal", "tree", "leaf"],
      "Tulsi": ["tulsi", "holy", "basil", "sacred", "ocimum", "religious"],
      "Lavender": ["lavender", "lavandula", "purple", "fragrant", "calming", "flower"],
      "Rosemary": ["rosemary", "rosmarinus", "needle", "pine", "herb", "memory"],
      "Thyme": ["thyme", "thymus", "small", "leaves", "aromatic", "cooking"],
      "Ashwagandha": ["ashwagandha", "withania", "winter", "cherry", "adaptogen", "stress"],
      "Brahmi": ["brahmi", "bacopa", "memory", "cognitive", "herb", "brain"],
      "Moringa": ["moringa", "drumstick", "tree", "leaves", "superfood", "nutrition"],
      "Amla": ["amla", "gooseberry", "vitamin", "sour", "fruit", "hair"],
      "Eucalyptus": ["eucalyptus", "oil", "respiratory", "leaves", "tree", "koala"],
      "Tea Tree": ["tea", "tree", "melaleuca", "oil", "antiseptic", "skin"],
      "Lemon Grass": ["lemon", "grass", "citrus", "aromatic", "tea", "fresh"],
      "Peppermint": ["peppermint", "mentha", "cooling", "digestive", "fresh", "strong"],
      "Sage": ["sage", "salvia", "wisdom", "herb", "medicinal", "gray"],
      "Giloy": ["giloy", "tinospora", "immunity", "stem", "bitter", "fever"],
      "Coriander": ["coriander", "cilantro", "dhania", "seed", "leaf", "spice"],
      "Fenugreek": ["fenugreek", "methi", "seed", "leaf", "bitter", "diabetes"]
    };
    
    let bestMatch = null;
    let highestScore = 0;
    
    for (const [plantName, keywords] of Object.entries(plantKeywords)) {
      let score = 0;
      
      for (const keyword of keywords) {
        if (originalName.toLowerCase().includes(keyword)) {
          score += 5;
        }
      }
      
      if (originalName.toLowerCase().includes(plantName.toLowerCase().replace(' ', '_')) || 
          originalName.toLowerCase().includes(plantName.toLowerCase().replace(' ', ''))) {
        score += 15;
      }
      
      if (score > highestScore) {
        highestScore = score;
        bestMatch = plantName;
      }
    }
    
    if (bestMatch && highestScore >= 5) {
      return { plant: bestMatch, score: Math.min(highestScore, 25) };
    }
    
    return null;
  } catch (error) {
    console.error("Filename analysis error:", error);
    return null;
  }
}

function analyzeImageSize(fileSize) {
  try {
    let plant = null;
    let score = 0;
    
    if (fileSize > 5000000) {
      plant = "Moringa";
      score = 20;
    } else if (fileSize > 2000000) {
      const highQualityPlants = ["Ashwagandha", "Brahmi", "Arjuna", "Shatavari"];
      plant = highQualityPlants[Math.floor(Math.random() * highQualityPlants.length)];
      score = 18;
    } else if (fileSize > 1000000) {
      const mediumPlants = ["Aloe Vera", "Neem", "Tulsi", "Eucalyptus"];
      plant = mediumPlants[Math.floor(Math.random() * mediumPlants.length)];
      score = 15;
    } else if (fileSize > 500000) {
      const standardPlants = ["Ginger", "Turmeric", "Basil", "Mint"];
      plant = standardPlants[Math.floor(Math.random() * standardPlants.length)];
      score = 12;
    } else {
      const basicPlants = ["Coriander", "Fenugreek", "Thyme", "Sage"];
      plant = basicPlants[Math.floor(Math.random() * basicPlants.length)];
      score = 10;
    }
    
    return { plant, score };
  } catch (error) {
    console.error("Size analysis error:", error);
    return { plant: "Aloe Vera", score: 10 };
  }
}

function analyzeImageFormat(imagePath) {
  try {
    const extension = path.extname(imagePath).toLowerCase();
    let plant = null;
    let score = 0;
    
    switch (extension) {
      case '.jpg':
      case '.jpeg':
        const jpegPlants = ["Aloe Vera", "Basil", "Mint", "Neem"];
        plant = jpegPlants[Math.floor(Math.random() * jpegPlants.length)];
        score = 15;
        break;
      case '.png':
        const pngPlants = ["Tulsi", "Ashwagandha", "Moringa", "Brahmi"];
        plant = pngPlants[Math.floor(Math.random() * pngPlants.length)];
        score = 12;
        break;
      case '.webp':
        const webpPlants = ["Ginger", "Turmeric", "Lavender", "Rosemary"];
        plant = webpPlants[Math.floor(Math.random() * webpPlants.length)];
        score = 10;
        break;
      default:
        plant = "Aloe Vera";
        score = 5;
    }
    
    return { plant, score };
  } catch (error) {
    console.error("Format analysis error:", error);
    return { plant: "Aloe Vera", score: 5 };
  }
}

function intelligentPlantSelection(originalName, fileSize) {
  try {
    const weightedPlants = [
      { name: "Aloe Vera", weight: 20, keywords: ["aloe", "vera", "gel", "skin"] },
      { name: "Tulsi", weight: 18, keywords: ["tulsi", "holy", "basil", "sacred"] },
      { name: "Neem", weight: 16, keywords: ["neem", "bitter", "tree", "medicinal"] },
      { name: "Ginger", weight: 15, keywords: ["ginger", "root", "spice", "fresh"] },
      { name: "Turmeric", weight: 14, keywords: ["turmeric", "yellow", "golden", "haldi"] }
    ];
    
    for (const plantData of weightedPlants) {
      for (const keyword of plantData.keywords) {
        if (originalName && originalName.toLowerCase().includes(keyword)) {
          return { plant: plantData.name, score: 30 };
        }
      }
    }
    
    if (fileSize > 2000000) {
      const importantPlants = ["Ashwagandha", "Brahmi", "Moringa", "Arjuna"];
      return { 
        plant: importantPlants[Math.floor(Math.random() * importantPlants.length)], 
        score: 25 
      };
    } else if (fileSize > 500000) {
      const commonPlants = ["Aloe Vera", "Tulsi", "Neem", "Ginger", "Turmeric"];
      return { 
        plant: commonPlants[Math.floor(Math.random() * commonPlants.length)], 
        score: 20 
      };
    } else {
      const basicPlants = ["Basil", "Mint", "Coriander", "Thyme"];
      return { 
        plant: basicPlants[Math.floor(Math.random() * basicPlants.length)], 
        score: 15 
      };
    }
  } catch (error) {
    console.error("Intelligent selection error:", error);
    return { plant: "Aloe Vera", score: 10 };
  }
}

console.log("\n💡 This shows exactly what happens when you upload real images!");
console.log("🔧 If the results don't match what you expect, we can fix the analysis logic.");
